datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id               String          @id @default(auto()) @map("_id") @db.ObjectId
  name             String?
  email            String?         @unique
  emailVerified    DateTime?
  image            String?
  admin            <PERSON>         @default(false)
  isDeveloper      Boolean         @default(false)
  headstealPremium Boolean         @default(false)
  accounts         Account[]
  licenseRequests  LicenseRequest[]
  licenses         License[]
}

model Account {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?  @db.String
  access_token      String?  @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?  @db.String
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Announcement {
  id          String   @id @default(uuid()) @map("_id")
  title       String
  description String
  footer      String?
  imageUrl    String?
  createdAt   DateTime @default(now())
}

model PartnershipApplication {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  userId          String    @unique @db.ObjectId
  username        String
  companyName     String
  websiteUrl      String
  reason          String
  experience      String
  additional      String?
  status          String    @default("pending")
  appliedAt       DateTime  @default(now())
  statusUpdatedAt DateTime?
}

model StaffApplication {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  userId          String    @unique @db.ObjectId
  fullName        String
  age             Int
  location        String
  availability    String
  position        String
  experience      String
  skills          String
  motivation      String
  portfolioUrl    String?
  additionalInfo  String?
  status          String    @default("pending")
  appliedAt       DateTime  @default(now())
  statusUpdatedAt DateTime?
}

model LicenseRequest {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  userId         String   @db.ObjectId
  spigotUsername String?
  screenshotUrl  String
  status         String   @default("pending")
  requestedAt    DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([requestedAt])
}

model License {
  id             String         @id @default(auto()) @map("_id") @db.ObjectId
  userId         String         @db.ObjectId
  spigotUsername String?
  licenseType    String         @default("headsteal")
  isActive       Boolean        @default(true)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  expiresAt      DateTime?
  user           User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  apiKeys        LicenseApiKey[]

  @@index([userId])
  @@index([licenseType])
  @@index([isActive])
}

model LicenseApiKey {
  id          String            @id @default(auto()) @map("_id") @db.ObjectId
  licenseId   String            @db.ObjectId
  keyHash     String            @unique
  serverIp    String
  serverName  String?
  isActive    Boolean           @default(true)
  lastUsed    DateTime?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  license     License           @relation(fields: [licenseId], references: [id], onDelete: Cascade)
}

model ApiKeyUsageLog {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  ipAddress  String
  status     Int
  userAgent  String?
  servername String?
  apiKey     String?  // just a string, no relation
  createdAt  DateTime @default(now())
}


model Plugin {
  id            String          @id @default(auto()) @map("_id") @db.ObjectId
  publisherId   String          @db.ObjectId
  publisherName String
  name          String
  description   String
  version       String
  releaseType   String          @default("release")
  releaseDate   DateTime        @default(now())
  updateDate    DateTime        @updatedAt
  fileLocation  String
  downloads     Int             @default(0)
  comments      PluginComment[]
}

model PluginComment {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  pluginId  String   @db.ObjectId
  userId    String   @db.ObjectId
  userName  String
  content   String
  createdAt DateTime @default(now())
  plugin    Plugin   @relation(fields: [pluginId], references: [id], onDelete: Cascade)
}


