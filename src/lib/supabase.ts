import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client with error handling
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Please add NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY to your .env file');
}

// Create client with defensive fallback for server components
let supabase: SupabaseClient | null = null;

// Init function for basic storage initialization
export async function initSupabaseStorage(): Promise<boolean> {
  if (!supabase || !supabaseUrl || !supabaseServiceKey) return false;

  try {
    // Basic storage initialization - can be extended for other storage needs
    const { data: buckets } = await supabase.storage.listBuckets();
    console.log('Supabase storage initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing Supabase storage:', error);
    return false;
  }
}

try {
  supabase = createClient(
    supabaseUrl || 'https://placeholder.supabase.co', 
    supabaseServiceKey || 'placeholder',
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    }
  );
} catch (error) {
  console.error('Error initializing Supabase client:', error);
}

export default supabase;


