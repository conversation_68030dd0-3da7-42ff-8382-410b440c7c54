import NextAuth from "next-auth";
import { NextResponse } from "next/server";

import { authConfig } from "@/lib/auth.config";
import { API_AUTH_PREFIX, AUTH_ROUTES, PROTECTED_ROUTES, DEVELOPER_ROUTES, PUBLIC_API_ROUTES } from "@/routes";

export const { auth } = NextAuth(authConfig);

export default auth(async (req) => {
  const pathname = req.nextUrl.pathname;

  const isAuth = req.auth;
  const email = req.auth?.user?.email;

  // Removed console.log for production

  const isAccessingApiAuthRoute = pathname.startsWith(API_AUTH_PREFIX);
  const isAccessingPublicApiRoute = PUBLIC_API_ROUTES.some(route => {
    return pathname.startsWith(route);
  });
  const isAccessingProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route));
  const isAccessingAuthRoute = AUTH_ROUTES.some(route => pathname.startsWith(route));
  const isAccessingAdminRoute = pathname.startsWith("/admin");
  const isAccessingDeveloperRoute = DEVELOPER_ROUTES.some(route => pathname.startsWith(route));

  // Allow NextAuth routes
  if (isAccessingApiAuthRoute) {
    return NextResponse.next();
  }

  // Allow public API routes
  if (isAccessingPublicApiRoute) {
    // Removed console.log for production
    return NextResponse.next();
  }

  if (isAccessingAdminRoute) {
    if (!isAuth) {
      console.log('Middleware - Not authenticated, redirecting to login.');
      return NextResponse.redirect(new URL("/missing-permission", req.url));
    }

    // Check admin status directly using the email
      // For admin routes, we'll only block users we know are not admins
      // If we can't determine the status, we'll allow access and let client-side handle it
      if (email) {
        const res = await fetch(`${req.nextUrl.origin}/api/admin-check`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email })
        });

        if (res.ok) {
          const data = await res.json();
          // Removed console.log for production

          if (data.admin === false) {
            // Removed console.log for production
            return NextResponse.redirect(new URL("/missing-permission", req.url));
          }
        }
    }
    return NextResponse.next();
  }

  if (isAccessingDeveloperRoute) {
    if (!isAuth) {
      // Removed console.log for production
      return NextResponse.redirect(new URL("/missing-permission", req.url));
    }

    // Check developer status directly using the email
    if (email) {
      const res = await fetch(`${req.nextUrl.origin}/api/developer-check`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });

      if (res.ok) {
        const data = await res.json();
        // Removed console.log for production

        if (data.isDeveloper === false) {
          // Removed console.log for production
          return NextResponse.redirect(new URL("/missing-permission", req.url));
        }
      }
    }
    return NextResponse.next();
  }

  if (isAccessingProtectedRoute) {
    if (!isAuth) {
      return NextResponse.redirect(new URL("/missing-permission", req.url));
    }
    return NextResponse.next();
  }

  if (isAccessingAuthRoute) {
    if (isAuth) {
      return NextResponse.redirect(new URL("/", req.url));
    }
    return NextResponse.next();
  }

  return NextResponse.next();
});

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
