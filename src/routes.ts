export const API_AUTH_PREFIX = "/api/auth";

export const AUTH_ROUTES = ["/client/login"];

export const PROTECTED_ROUTES = [
  "/partnership",
  "/plugins",
  "/staffapplication",
  "/license"
]

export const DEVELOPER_ROUTES = [
  "/developer-console"
]

// Public API routes that don't require authentication
export const PUBLIC_API_ROUTES = [
  // Apps routes removed
]

// Admin routes are already handled separately in middleware.ts
