import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import supabase, { initSupabaseStorage } from "@/lib/supabase";

export async function GET(req: NextRequest) {
  try {
    // Check authentication and admin status
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Get Supabase configuration
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 10) + '...'; // Truncated for security
    
    // Check if Supabase client is initialized
    if (!supabase) {
      return NextResponse.json({
        success: false,
        message: "Supabase client is not initialized",
        config: {
          supabaseUrl,
          supabaseKeyProvided: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
          truncatedKey: supabaseServiceKey
        }
      }, { status: 500 });
    }
    
    // Initialize storage
    const storageInitialized = await initSupabaseStorage();
    
    // List buckets to test connection
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      return NextResponse.json({
        success: false,
        message: "Failed to list Supabase buckets",
        error: bucketsError.message,
        config: {
          supabaseUrl,
          supabaseKeyProvided: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
          truncatedKey: supabaseServiceKey
        },
        storageInitialized
      }, { status: 500 });
    }
    
    // Basic bucket information
    const bucketsInfo = buckets?.map(b => ({ name: b.name, id: b.id })) || [];
    
    // Test uploading a tiny file
    const testUploadResult = await testFileUpload();
    
    return NextResponse.json({
      success: true,
      message: "Supabase connection successful",
      config: {
        supabaseUrl,
        supabaseKeyProvided: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        truncatedKey: supabaseServiceKey
      },
      storageInitialized,
      buckets: {
        count: buckets?.length || 0,
        list: buckets || []
      },
      pluginsBucket: {
        exists: !!pluginsBucket,
        details: pluginsBucket || null,
        files: pluginsFiles
      },
      testUpload: testUploadResult
    });
  } catch (error) {
    console.error("Error testing Supabase connection:", error);
    return NextResponse.json({
      success: false,
      message: "Failed to connect to Supabase",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

async function testFileUpload() {
  try {
    if (!supabase) {
      return { success: false, error: "Supabase client is not initialized" };
    }
    
    // Create a tiny test file
    const testContent = new Uint8Array([0, 1, 2, 3]);
    const testFileName = `test-${Date.now()}.bin`;
    
    // Upload the test file (using a generic bucket for testing)
    const { data, error } = await supabase.storage
      .from('test-bucket')
      .upload(`${testFileName}`, testContent, {
        upsert: true,
      });
      
    if (error) {
      return { 
        success: false, 
        error: error.message
      };
    }
    
    // Get the URL
    const { data: urlData } = supabase.storage
      .from('test-bucket')
      .getPublicUrl(`${testFileName}`);
    
    // Clean up - delete the test file (don't wait for it)
    supabase.storage
      .from('plugins')
      .remove([`public/${testFileName}`])
      .then(() => console.log('Test file deleted'))
      .catch(err => console.warn('Could not delete test file:', err));
    
    return { 
      success: true, 
      fileUrl: urlData.publicUrl,
      filePath: `public/${testFileName}`
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : String(error)
    };
  }
} 