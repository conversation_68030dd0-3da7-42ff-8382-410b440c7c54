import { NextResponse } from 'next/server'
import prisma from '@/lib/db'

export async function POST(req: Request) {
  try {
    const body = await req.json()

    const {
      ipAddress,
      status,
      userAgent = 'unknown',
      servername,
      apiKey,
    } = body

    if (!ipAddress || typeof status !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields: ipAddress or status' },
        { status: 400 }
      )
    }

    await prisma.apiKeyUsageLog.create({
      data: {
        ipAddress,
        status,
        userAgent,
        servername,
        apiKey,
      },
    })

    return NextResponse.json({ success: true }, { status: 201 })
  } catch (error) {
    console.error('Error creating usage log:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
