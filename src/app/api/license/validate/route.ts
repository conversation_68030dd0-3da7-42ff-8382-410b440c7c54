import { NextResponse } from 'next/server'
import prisma from '@/lib/db'
import { isValidIPAddress } from '@/lib/license-errors'
import crypto from 'crypto'

function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex')
}

export async function POST(req: Request) {
  try {
    const { apiKey, serverIp } = await req.json()

    if (!apiKey || !serverIp) {
      return NextResponse.json({ valid: false }, { status: 400 })
    }
    if (!isValidIPAddress(serverIp)) {
      return NextResponse.json({ valid: false }, { status: 400 })
    }

    const keyHash = apiKey

    const record = await prisma.licenseApiKey.findFirst({
      where: {
        keyHash,
        serverIp,
        isActive: true
      }
    })

    return NextResponse.json({ valid: <PERSON><PERSON><PERSON>(record) })
  } catch (err) {
    console.error('API key validation error', err)
    return NextResponse.json({ valid: false }, { status: 500 })
  }
}
