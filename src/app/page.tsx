"use client";

import { Header } from "@/components/Header";
import Footer from "@/components/Footer";
import { useEffect, useState } from "react";
import HeroSection from "@/components/homepage/HeroSection";

import FeaturesSection from "@/components/homepage/FeaturesSection";
import AnimatedBackground from "@/components/homepage/AnimatedBackground";
import FAQs from "@/components/Faqs";
import { OurProjects } from "@/components/Cards";

export default function AppStore() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <div className="w-full scroll-smooth bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 dark:from-purple-950 dark:via-blue-950 dark:to-indigo-950 min-h-screen">
      <Header type="normal"/>

      <AnimatedBackground />

      <div className="relative z-10">
        {/* Hero Section */}
        <HeroSection />
        
        {/* Features Section */}
        <FeaturesSection />

        {/* FAQ Section */}
        <FAQs />

        <Footer />
      </div>
    </div>
  );
}
