@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Modern Dark Theme Color System */
  --background: #000000;
  --foreground: #ffffff;

  /* Surface colors */
  --surface-primary: #0a0a0a;
  --surface-secondary: #111111;
  --surface-tertiary: #1a1a1a;
  --surface-elevated: #222222;

  /* Border colors */
  --border-primary: #333333;
  --border-secondary: #444444;
  --border-accent: #555555;

  /* Text colors */
  --text-primary: #ffffff;
  --text-secondary: #e5e5e5;
  --text-tertiary: #a3a3a3;
  --text-muted: #737373;

  /* Accent colors */
  --accent-primary: #3b82f6;
  --accent-secondary: #f97316;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-error: #ef4444;

  /* Gradient colors */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #f97316 100%);
  --gradient-secondary: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --gradient-accent: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);

  /* Glass morphism */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.5);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.25);
}

body {
  color: var(--text-primary);
  background: var(--background);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  scroll-behavior: smooth;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Modern Typography Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-secondary);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 8px;
  border: 2px solid var(--surface-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #2563eb, #ea580c);
}

::-webkit-scrollbar-corner {
  background: var(--surface-secondary);
}

/* Modern Navigation */
.nav-link {
  color: var(--text-tertiary);
  background-color: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

.nav-link:hover {
  color: var(--text-primary);
  background-color: var(--glass-bg);
  backdrop-filter: blur(10px);
  transform: translateY(-1px);
}

.nav-link.active {
  color: transparent;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-color: transparent;
}

.nav-link.active::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: 1px;
}


.project-box {
  margin-top: 80px;
}

html {
  scroll-behavior: smooth;
}


/* Modern Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes bubble {
  0% {
    transform: translateY(0);
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100vh);
    opacity: 0;
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Glass morphism utility */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-glow {
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Modern Layout Utilities */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.grid-center {
  display: grid;
  place-items: center;
}

/* Responsive Grid Patterns */
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
  gap: 1.5rem;
}

.grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  grid-auto-rows: masonry;
  gap: 1.5rem;
}

/* Container Queries Support */
@container (min-width: 400px) {
  .container-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container (min-width: 600px) {
  .container-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}
