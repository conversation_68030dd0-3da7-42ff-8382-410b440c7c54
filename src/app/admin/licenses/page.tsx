'use client';
import { useSession } from 'next-auth/react';
import { Header } from '@/components/Header';
import { useRouter } from 'next/navigation';
import { useFetchLicenseRequests } from '@/hooks/license/useFetchLicenseRequests';
import { useUpdateLicenseStatus } from '@/hooks/license/useUpdateLicenseStatus';
import LoadingDots from '@/components/animations/Loading';
import { FileText } from 'lucide-react';

export default function LicenseAdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { requests, loading } = useFetchLicenseRequests();
  const { updateStatus, processingId } = useUpdateLicenseStatus();

  if (status === 'loading' || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingDots />
      </div>
    );
  }

  if (!session?.user?.admin) {
    router.push('/');
    return null;
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Approved</span>;
      case 'rejected':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Rejected</span>;
      default:
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <Header type="admin" />
      <div className="p-6">
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">License Requests</h2>
            <p className="text-gray-400">Manage user license applications for Headsteal Premium</p>
          </div>
          <button
            onClick={() => router.push('/admin/licenses/logs')} 
            className="inline-flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium bg-gray-700 text-white hover:bg-gray-600 transition-colors"
          >
            <FileText className="w-4 h-4" />
            Logs
          </button>
        </div>

        {requests.length === 0 ? (
          <div className="bg-gray-800 rounded-lg p-8 text-center">
            <p className="text-gray-400 text-lg">No license requests found</p>
          </div>
        ) : (
          <div className="bg-gray-800 rounded-lg overflow-hidden shadow-lg">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Spigot Username</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Screenshot</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Requested</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-gray-800 divide-y divide-gray-700">
                  {requests.map(req => (
                    <tr key={req.id} className="hover:bg-gray-750 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div>
                          <div className="text-white font-medium">
                            {req.userName || 'Unknown User'}
                          </div>
                          <div className="text-gray-400 text-xs">
                            {req.userEmail || `ID: ${req.userId.slice(-8)}...`}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white font-medium">
                        {req.spigotUsername}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <a
                          href={req.screenshotUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                        >
                          View Screenshot
                        </a>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(req.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        {formatDate(req.requestedAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                        {req.status === 'pending' && (
                          <>
                            <button
                              disabled={processingId === req.id}
                              onClick={() => updateStatus(req.id, 'approved')}
                              className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                              {processingId === req.id ? 'Processing...' : 'Approve'}
                            </button>
                            <button
                              disabled={processingId === req.id}
                              onClick={() => updateStatus(req.id, 'rejected')}
                              className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                              {processingId === req.id ? 'Processing...' : 'Reject'}
                            </button>
                          </>
                        )}
                        {req.status !== 'pending' && (
                          <span className="text-gray-500 text-sm">No actions available</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
