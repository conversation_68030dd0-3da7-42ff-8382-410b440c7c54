'use client';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/Header';
import LoadingDots from '@/components/animations/Loading';

interface LogEntry {
  id: string;
  ipAddress: string;
  status: number;
  userAgent?: string;
  servername?: string;
  apiKey?: string;
  createdAt: string;
}

export default function LogAdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'authenticated') {
      fetch('/api/license/logs')
        .then(res => res.json())
        .then(data => setLogs(data))
        .finally(() => setLoading(false));
    }
  }, [status]);

  if (status === 'loading' || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900">
        <LoadingDots />
      </div>
    );
  }

  if (!session?.user?.admin) {
    router.push('/');
    return null;
  }

  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

  const getStatusColor = (status: number) => {
    if (status >= 500) return 'text-red-400';
    if (status >= 400) return 'text-yellow-400';
    return 'text-green-400';
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <Header type="admin" />
      <div className="p-6">
        <div className="mb-6">
          <h2 className="text-3xl font-bold text-white mb-2">API Usage Logs</h2>
          <p className="text-gray-400">Monitor endpoint usage and troubleshoot API access</p>
        </div>

        {logs.length === 0 ? (
          <div className="bg-gray-800 rounded-lg p-8 text-center">
            <p className="text-gray-400 text-lg">No logs found</p>
          </div>
        ) : (
          <div className="bg-gray-800 rounded-lg overflow-hidden shadow-lg">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">IP</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Server Name</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">API Key</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Status</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">User Agent</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Time</th>
                  </tr>
                </thead>
                <tbody className="bg-gray-800 divide-y divide-gray-700">
                  {logs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-750 transition-colors">
                      <td className="px-4 py-3 text-sm text-white">{log.ipAddress}</td>
                      <td className="px-4 py-3 text-sm text-blue-300">{log.servername || '-'}</td>
                      <td className="px-4 py-3 text-sm text-purple-300">{log.apiKey || '-'}</td>
                      <td className={`px-4 py-3 text-sm font-medium ${getStatusColor(log.status)}`}>
                        {log.status}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 truncate max-w-xs">{log.userAgent || '-'}</td>
                      <td className="px-4 py-3 text-sm text-gray-400">{formatDate(log.createdAt)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
