"use client";

import { motion } from "framer-motion";
import { Zap, Shield, Heart, Users, Smartphone, Star, Sparkles, Lock, Rocket } from "lucide-react";

const features = [
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Optimized for speed and performance across all devices"
  },
  {
    icon: Shield,
    title: "Secure & Private",
    description: "Your data is protected with enterprise-grade security"
  },
  {
    icon: Heart,
    title: "User-Friendly",
    description: "Intuitive design that makes complex tasks simple"
  },
  {
    icon: Users,
    title: "Community Driven",
    description: "Built with feedback from our amazing user community"
  },
  {
    icon: Smartphone,
    title: "Cross-Platform",
    description: "Works seamlessly on mobile, tablet, and desktop"
  },
  {
    icon: Star,
    title: "Premium Quality",
    description: "Crafted with attention to detail and user experience"
  },
  {
    icon: Sparkles,
    title: "AI-Powered",
    description: "Enhanced with artificial intelligence for smarter workflows"
  },
  {
    icon: Lock,
    title: "Privacy First",
    description: "Your personal data stays private and under your control"
  },
  {
    icon: Rocket,
    title: "Regular Updates",
    description: "Continuous improvements and new features every month"
  }
];

export default function FeaturesSection() {
  return (
    <section className="py-20 px-8 bg-white/10 dark:bg-gray-900/10 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Why Choose AveHub?
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Experience the difference with our carefully crafted solutions designed for the modern world
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              className="text-center p-8 bg-white/20 dark:bg-gray-800/20 backdrop-blur-sm rounded-3xl border border-purple-200/30 dark:border-purple-700/30 hover:border-purple-400/50 transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
            >
              <motion.div
                className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl mb-6"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <feature.icon className="w-8 h-8 text-white" />
              </motion.div>
              <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">{feature.title}</h3>
              <p className="text-gray-600 dark:text-gray-400">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="group relative px-10 py-4 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 text-white rounded-2xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 flex items-center gap-3 text-lg font-semibold mx-auto"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Rocket size={24} />
            Start Exploring Apps
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-purple-400 via-blue-400 to-indigo-400 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"
            />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
