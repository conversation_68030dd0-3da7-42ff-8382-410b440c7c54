import React from "react";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center rounded-xl font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:opacity-50 disabled:pointer-events-none hover-lift';

    const variantClasses = {
      default: 'bg-gradient-primary text-white hover:shadow-lg hover-glow',
      destructive: 'bg-accent-error text-white hover:bg-red-600 hover:shadow-lg',
      outline: 'border border-border-primary bg-transparent hover:bg-surface-secondary hover:border-border-accent',
      secondary: 'bg-surface-elevated text-text-primary hover:bg-surface-tertiary',
      ghost: 'hover:bg-surface-secondary hover:text-text-primary',
      link: 'text-accent-primary underline-offset-4 hover:underline hover:text-accent-secondary',
    };

    const sizeClasses = {
      default: 'h-11 py-3 px-6 text-sm',
      sm: 'h-9 px-4 rounded-lg text-xs',
      lg: 'h-12 px-8 rounded-xl text-base',
      icon: 'h-11 w-11',
    };

    return (
      <button
        ref={ref}
        className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className || ''}`}
        {...props}
      />
    );
  }
);

Button.displayName = "Button";

export { Button }; 