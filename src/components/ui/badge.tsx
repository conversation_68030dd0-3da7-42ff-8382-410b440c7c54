import React from "react";
import clsx from "clsx";

interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: "default" | "secondary" | "outline" | "destructive";
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, children, variant = "default", ...props }, ref) => {
    const variantClasses: Record<string, string> = {
      default: "bg-gradient-primary text-white hover:shadow-lg hover-glow",
      secondary: "bg-surface-tertiary text-text-secondary hover:bg-surface-elevated",
      outline: "border border-border-primary text-text-primary bg-transparent hover:bg-surface-secondary",
      destructive: "bg-accent-error text-white hover:bg-red-600",
    };

    return (
      <div
        ref={ref}
        className={clsx(
          "inline-flex items-center gap-1 rounded-full px-3 py-1.5 text-xs font-medium transition-all duration-300 hover-lift",
          variantClasses[variant],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Badge.displayName = "Badge";

export { Badge, type BadgeProps };
